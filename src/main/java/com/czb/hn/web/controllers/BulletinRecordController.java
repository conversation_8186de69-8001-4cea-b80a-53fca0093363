package com.czb.hn.web.controllers;

import com.czb.hn.dto.bulletin.BulletinGenerationRecordDto;
import com.czb.hn.dto.bulletin.BulletinPushRecordDto;
import com.czb.hn.dto.common.PageResult;
import com.czb.hn.dto.response.ApiResponse;
import com.czb.hn.service.bulletin.BulletinRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 简报记录控制器
 */
@RestController
@RequestMapping("/bulletin/records")
@Tag(name = "简报记录管理", description = "简报生成记录和推送记录的管理接口")
public class BulletinRecordController {
    
    private static final Logger log = LoggerFactory.getLogger(BulletinRecordController.class);
    
    private final BulletinRecordService bulletinRecordService;

    public BulletinRecordController(BulletinRecordService bulletinRecordService) {
        this.bulletinRecordService = bulletinRecordService;
    }

    /**
     * 查询简报生成记录列表
     *
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param bulletinType 简报类型（可选）
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 简报生成记录分页结果
     */
    @GetMapping("/search")
    @Operation(summary = "查询简报生成记录", description = "根据时间范围和/或简报类型查询简报生成记录，所有条件均为可选，支持分页")
    public ResponseEntity<ApiResponse<PageResult<BulletinGenerationRecordDto>>> searchGenerationRecords(
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @Parameter(description = "简报类型（DAILY、WEEKLY、MONTHLY）") @RequestParam(required = false) String bulletinType,
            @Parameter(description = "页码，从1开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        try {
            // 调用Service层获取分页DTO数据
            PageResult<BulletinGenerationRecordDto> pageResult = bulletinRecordService.searchGenerationRecords(
                startTime, 
                endTime,
                bulletinType,
                page + 1,
                size
            );
            
            // 返回成功响应
            return ResponseEntity.ok(ApiResponse.success(pageResult));
        } catch (Exception e) {
            log.error("查询简报生成记录失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("查询简报生成记录失败: " + e.getMessage()));
        }
    }
    
    /**
     * 根据生成记录ID获取推送记录
     *
     * @param generationId 生成记录ID
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 推送记录分页结果
     */
    @GetMapping("/{generationId}/pushes")
    @Operation(summary = "获取简报推送记录", description = "获取指定简报生成记录的所有推送记录，支持分页")
    public ResponseEntity<ApiResponse<PageResult<BulletinPushRecordDto>>> getPushRecordsByGenerationId(
            @Parameter(description = "生成记录ID") @PathVariable Long generationId,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        try {
            // 调用Service层获取分页DTO数据
            PageResult<BulletinPushRecordDto> pageResult = bulletinRecordService.getPushRecordsByGenerationId(
                generationId, 
                page, 
                size
            );
            
            // 返回成功响应
            return ResponseEntity.ok(ApiResponse.success(pageResult));
        } catch (Exception e) {
            log.error("获取推送记录失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取推送记录失败: " + e.getMessage()));
        }
    }
    
    /**
     * 手动推送简报
     *
     * @param generationId 生成记录ID
     * @return 推送记录ID列表
     */
    @PostMapping("/{generationId}/push")
    @Operation(summary = "手动推送简报", description = "手动推送指定的简报到邮箱或短信")
    public ResponseEntity<ApiResponse<List<Long>>> manualPushBulletin(
            @Parameter(description = "生成记录ID") @PathVariable Long generationId) {
        try {
            // 调用Service层执行推送
            List<Long> pushIds = bulletinRecordService.manualPushBulletin(generationId);
            // 返回成功响应
            return ResponseEntity.ok(ApiResponse.success(pushIds));
        } catch (Exception e) {
            log.error("手动推送简报失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("手动推送简报失败: " + e.getMessage()));
        }
    }

    /**
     * 下载简报内容
     *
     * @param generationId 生成记录ID
     * @return 简报内容
     */
    @GetMapping("/{generationId}/download")
    @Operation(summary = "下载简报内容", description = "下载指定简报生成记录的PDF内容")
    public ResponseEntity<byte[]> downloadBulletinContent(
            @Parameter(description = "生成记录ID") @PathVariable Long generationId) {
        try {
            // 调用Service层获取DTO
            BulletinGenerationRecordDto record = bulletinRecordService.getGenerationRecordById(generationId);

            // 检查记录是否存在
            if (record == null) {
                return ResponseEntity.notFound().build();
            }

            // 调用Service层获取简报内容
            byte[] content = bulletinRecordService.getBulletinContent(generationId);

            // 检查内容是否为空
            if (content == null || content.length == 0) {
                return ResponseEntity.noContent().build();
            }

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData("attachment", record.bulletinTitle() + ".pdf");

            // 返回文件内容
            return new ResponseEntity<>(content, headers, HttpStatus.OK);
        } catch (Exception e) {
            log.error("下载简报内容失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
} 