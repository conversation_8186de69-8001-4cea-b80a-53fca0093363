package com.czb.hn.service.business;

import com.czb.hn.dto.alert.AlertPushDetailCreateDto;
import com.czb.hn.dto.alert.AlertPushDetailResponseDto;
import com.czb.hn.jpa.securadar.entity.AlertResult;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Alert Push Service Interface
 * Manages alert notification push operations including creation, querying, and
 * actual push delivery
 * Supports multi-tenant isolation and various notification channels
 */
public interface AlertPushService {


        /**
         * Get push details by alert ID
         * 
         * @param alertId Alert ID to search for
         * @return List of push details for the alert
         */
        List<AlertPushDetailResponseDto> getPushDetailsByAlertId(Long alertId);

        /**
         * Send email notification (placeholder implementation)
         * 
         * @param email   Email address
         * @param subject Email subject
         * @param content Email content
         * @return Async result of email sending operation
         */
        CompletableFuture<Boolean> sendEmailNotification(String email, String subject, String content);

        /**
         * Send SMS notification (placeholder implementation)
         * 
         * @param phoneNumber Phone number
         * @param message     SMS message content
         * @return Async result of SMS sending operation
         */
        CompletableFuture<Boolean> sendSmsNotification(String phoneNumber, String message);

        /**
         * Create push records for an alert and recipient (for batch notifications)
         *
         * @param alert     Alert result to create push records for
         * @param recipient Recipient information
         */
        void createPushRecordsForAlert(AlertResult alert, Object recipient);
}
